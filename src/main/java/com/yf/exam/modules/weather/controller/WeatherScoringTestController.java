package com.yf.exam.modules.weather.controller;

import com.yf.exam.core.api.controller.BaseController;
import com.yf.exam.core.api.dto.BaseIdReqDTO;
import com.yf.exam.core.utils.ApiRest;
import com.yf.exam.modules.weather.entity.WeatherScoringResult;
import com.yf.exam.modules.weather.service.WeatherHistoryExamResultService;
import com.yf.exam.modules.weather.scoring.service.WeatherScoringResultService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 天气评分测试控制器
 * 用于调试和测试评分数据结构
 *
 * <AUTHOR> Assistant
 * @date 2025-01-29
 */
@Slf4j
@RestController
@RequestMapping("/weather/scoring/test")
@Api(tags = "天气评分测试")
public class WeatherScoringTestController extends BaseController {

    @Autowired
    private WeatherHistoryExamResultService weatherHistoryExamResultService;

    @Autowired
    private WeatherScoringResultService weatherScoringResultService;

    /**
     * 测试获取考试结果详情
     */
    @ApiOperation("测试获取考试结果详情")
    @GetMapping("/exam-result/{examId}")
    public ApiRest<Map<String, Object>> testGetExamResult(
            @ApiParam("考试ID") @PathVariable String examId) {
        
        try {
            Map<String, Object> result = weatherHistoryExamResultService.getExamResult(examId);
            
            // 打印调试信息
            log.info("=== 考试结果数据结构调试 ===");
            log.info("考试ID: {}", examId);
            
            if (result.containsKey("gradingDetails")) {
                Map<String, Object> gradingDetails = (Map<String, Object>) result.get("gradingDetails");
                log.info("gradingDetails存在，键: {}", gradingDetails.keySet());
                
                if (gradingDetails.containsKey("detailResults")) {
                    Map<String, Object> detailResults = (Map<String, Object>) gradingDetails.get("detailResults");
                    log.info("detailResults存在，键: {}", detailResults.keySet());
                    
                    if (detailResults.containsKey("precipitationArea")) {
                        Map<String, Object> precipitationArea = (Map<String, Object>) detailResults.get("precipitationArea");
                        log.info("precipitationArea存在，键: {}", precipitationArea.keySet());
                        
                        if (precipitationArea.containsKey("stationDetails")) {
                            Object stationDetails = precipitationArea.get("stationDetails");
                            if (stationDetails instanceof java.util.List) {
                                java.util.List<?> stationList = (java.util.List<?>) stationDetails;
                                log.info("stationDetails存在，站点数量: {}", stationList.size());
                            } else {
                                log.info("stationDetails存在但不是List类型: {}", stationDetails.getClass());
                            }
                        } else {
                            log.warn("precipitationArea中不包含stationDetails");
                        }
                    } else {
                        log.warn("detailResults中不包含precipitationArea");
                    }
                } else {
                    log.warn("gradingDetails中不包含detailResults");
                }
                
                if (gradingDetails.containsKey("precipitationScoringDetails")) {
                    Map<String, Object> precipitationScoringDetails = (Map<String, Object>) gradingDetails.get("precipitationScoringDetails");
                    log.info("precipitationScoringDetails存在，键: {}", precipitationScoringDetails.keySet());
                } else {
                    log.warn("gradingDetails中不包含precipitationScoringDetails");
                }
            } else {
                log.warn("考试结果中不包含gradingDetails");
            }
            
            return success("获取成功", result);
            
        } catch (Exception e) {
            log.error("测试获取考试结果失败", e);
            return failure("获取失败：" + e.getMessage());
        }
    }

    /**
     * 测试获取评分结果详情
     */
    @ApiOperation("测试获取评分结果详情")
    @GetMapping("/scoring-result/{answerId}")
    public ApiRest<WeatherScoringResult> testGetScoringResult(
            @ApiParam("答案ID") @PathVariable String answerId) {
        
        try {
            WeatherScoringResult result = weatherScoringResultService.getByAnswerId(answerId);
            
            if (result != null) {
                log.info("=== 评分结果数据结构调试 ===");
                log.info("答案ID: {}", answerId);
                log.info("评分结果ID: {}", result.getId());
                log.info("最终得分: {}", result.getFinalScore());
                
                if (result.getDetailResults() != null) {
                    log.info("detailResults存在，键: {}", result.getDetailResults().keySet());
                    
                    if (result.getDetailResults().containsKey("precipitationArea")) {
                        Map<String, Object> precipitationArea = (Map<String, Object>) result.getDetailResults().get("precipitationArea");
                        log.info("precipitationArea存在，键: {}", precipitationArea.keySet());
                        
                        if (precipitationArea.containsKey("stationDetails")) {
                            Object stationDetails = precipitationArea.get("stationDetails");
                            if (stationDetails instanceof java.util.List) {
                                java.util.List<?> stationList = (java.util.List<?>) stationDetails;
                                log.info("stationDetails存在，站点数量: {}", stationList.size());
                            }
                        }
                    }
                } else {
                    log.warn("评分结果中不包含detailResults");
                }
                
                return success("获取成功", result);
            } else {
                return failure("未找到对应的评分结果");
            }
            
        } catch (Exception e) {
            log.error("测试获取评分结果失败", e);
            return failure("获取失败：" + e.getMessage());
        }
    }

    /**
     * 测试数据结构
     */
    @ApiOperation("测试数据结构")
    @PostMapping("/data-structure")
    public ApiRest<Map<String, Object>> testDataStructure(@RequestBody BaseIdReqDTO reqDTO) {
        
        try {
            String examId = reqDTO.getId();
            
            // 获取考试结果
            Map<String, Object> examResult = weatherHistoryExamResultService.getExamResult(examId);
            
            // 构建调试信息
            Map<String, Object> debugInfo = new java.util.HashMap<>();
            debugInfo.put("examId", examId);
            debugInfo.put("hasGradingDetails", examResult.containsKey("gradingDetails"));
            
            if (examResult.containsKey("gradingDetails")) {
                Map<String, Object> gradingDetails = (Map<String, Object>) examResult.get("gradingDetails");
                debugInfo.put("gradingDetailsKeys", gradingDetails.keySet());
                
                if (gradingDetails.containsKey("detailResults")) {
                    Map<String, Object> detailResults = (Map<String, Object>) gradingDetails.get("detailResults");
                    debugInfo.put("detailResultsKeys", detailResults.keySet());
                    
                    if (detailResults.containsKey("precipitationArea")) {
                        Map<String, Object> precipitationArea = (Map<String, Object>) detailResults.get("precipitationArea");
                        debugInfo.put("precipitationAreaKeys", precipitationArea.keySet());
                        debugInfo.put("hasStationDetails", precipitationArea.containsKey("stationDetails"));
                        
                        if (precipitationArea.containsKey("stationDetails")) {
                            Object stationDetails = precipitationArea.get("stationDetails");
                            if (stationDetails instanceof java.util.List) {
                                java.util.List<?> stationList = (java.util.List<?>) stationDetails;
                                debugInfo.put("stationCount", stationList.size());
                                if (!stationList.isEmpty()) {
                                    debugInfo.put("firstStationKeys", 
                                        ((Map<String, Object>) stationList.get(0)).keySet());
                                }
                            }
                        }
                    }
                }
            }
            
            return success("调试信息获取成功", debugInfo);
            
        } catch (Exception e) {
            log.error("测试数据结构失败", e);
            return failure("测试失败：" + e.getMessage());
        }
    }
}
